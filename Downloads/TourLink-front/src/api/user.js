let instance = null

// 用户服务专用请求函数，使用端口9085
const userRequest = async (url, options = {}) => {
  const controller = new AbortController()
  const timeoutId = setTimeout(() => controller.abort(), 10000)

  try {
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      },
      signal: controller.signal
    }

    // 合并headers，确保自定义headers不被覆盖
    const mergedOptions = {
      ...defaultOptions,
      ...options,
      headers: {
        ...defaultOptions.headers,
        ...options.headers
      }
    }

    // 如果是FormData，不设置Content-Type
    if (mergedOptions.body instanceof FormData) {
      delete mergedOptions.headers['Content-Type']
    }

    const response = await fetch(`http://localhost:9085${url}`, mergedOptions)

    // 处理不同的响应状态
    switch (response.status) {
      case 401:
        localStorage.removeItem('token')
        window.location.href = '/login'
        throw new Error('登录已过期')
      case 403:
        throw new Error('403错误，检查是否有输入错误')
      case 429:
        throw new Error('请求过于频繁')
    }

    // 尝试解析JSON，如果失败则返回原始响应
    let data
    try {
      data = await response.json()
    } catch (e) {
      if (!response.ok) {
        throw new Error(`请求失败: ${response.status}`)
      }
      return response
    }

    if (!response.ok) {
      throw new Error(data.message || `请求失败: ${response.status}`)
    }

    return data
  } catch (error) {
    const errorMessage = error.message || '未知错误'
    console.error(`请求失败: ${url}`, errorMessage)

    if (error.name === 'AbortError') {
      throw new Error('请求超时')
    }

    // 对于404错误，提供更友好的错误信息
    if (errorMessage.includes('404')) {
      throw new Error('请求的资源不存在')
    }

    throw error
  } finally {
    clearTimeout(timeoutId)
  }
}

class UserApi {
  constructor() {
    if (instance) {
      return instance
    }
    instance = this
  }

  // 用户基本操作
  register(data) {
    return userRequest('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  login(data) {
    return userRequest('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify(data)
    })
  }

  getUserInfo(id) {
    return userRequest(`/api/users/${id}`)
  }

  updateUser(id, data) {
    return userRequest(`/api/users/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  // 用户档案操作
  getProfile(userId) {
    return userRequest(`/api/user-profiles/user/${userId}`)
  }

  updateProfile(id, data) {
    return userRequest(`/api/user-profiles/${id}`, {
      method: 'PUT',
      body: JSON.stringify(data)
    })
  }

  // 用户角色操作
  getUserRoles(userId) {
    return userRequest(`/api/user-roles/user/${userId}`)
  }
  
  // 获取当前用户信息（使用存储的用户ID）
  getCurrentUser() {
    // 从localStorage获取用户ID
    const userId = localStorage.getItem('userId')
    if (!userId) {
      return Promise.reject(new Error('未找到用户ID'))
    }
    return this.getUserInfo(userId)
  }
  
  // 用户收藏操作
  addFavorite(userId, attractionId) {
    return userRequest(`/api/users/${userId}/favorites/${attractionId}`, {
      method: 'POST'
    })
  }

  removeFavorite(userId, attractionId) {
    return userRequest(`/api/users/${userId}/favorites/${attractionId}`, {
      method: 'DELETE'
    })
  }

  getUserFavorites(userId) {
    return userRequest(`/api/users/${userId}/favorites`)
  }

  // 用户评论操作
  getUserReviews(userId) {
    return userRequest(`/api/users/${userId}/reviews`)
  }
}

export const userApi = new UserApi()
import { defineStore } from 'pinia'
import { userApi } from '../api/user'


export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    token: localStorage.getItem('token'),
    loading: false,
    initialized: false // 添加初始化状态标记
  }),

  getters: {
    isLoggedIn: (state) => !!state.token && !!state.userInfo,
    userId: (state) => state.userInfo?.id || localStorage.getItem('userId')
  },

  actions: {
    // 初始化用户状态 - 应用启动时调用
    async initializeAuth() {
      if (this.initialized) return

      const token = localStorage.getItem('token')
      const userId = localStorage.getItem('userId')

      if (token && userId) {
        this.token = token
        try {
          // 验证token有效性并获取用户信息
          await this.fetchUserInfo()
          console.log('用户状态已恢复')
        } catch (error) {
          console.log('Token已过期或无效，清除登录状态')
          this.logout()
        }
      }

      this.initialized = true
    },

    // 验证token有效性
    async validateToken() {
      if (!this.token) return false

      try {
        const userId = localStorage.getItem('userId')
        if (!userId) return false

        // 尝试获取用户信息来验证token
        await userApi.getUserInfo(userId)
        return true
      } catch (error) {
        console.log('Token验证失败:', error.message)
        return false
      }
    },

    async register(credentials) {
      this.loading = true
      try {
        const response = await userApi.register(credentials)
        // 注册成功后不自动登录，返回响应让用户去登录页面
        return response
      } finally {
        this.loading = false
      }
    },

    async login(credentials) {
      this.loading = true
      try {
        const response = await userApi.login(credentials)

        // 设置登录状态
        this.token = response.token
        localStorage.setItem('token', response.token)

        // 存储用户ID，用于后续获取用户信息
        if (response.userId) {
          localStorage.setItem('userId', response.userId)
        }

        // 获取用户详细信息
        await this.fetchUserInfo()

        console.log('登录成功，用户状态已更新')
        return response
      } catch (error) {
        // 登录失败时清理状态
        this.logout()
        throw error
      } finally {
        this.loading = false
      }
    },

    async fetchUserInfo() {
      if (!this.token) {
        console.log('没有token，无法获取用户信息')
        return
      }

      try {
        // 使用存储的用户ID获取用户信息
        const userId = localStorage.getItem('userId')
        if (!userId) {
          throw new Error('未找到用户ID')
        }

        const userInfo = await userApi.getUserInfo(userId)
        this.userInfo = userInfo
        console.log('用户信息获取成功:', userInfo.username)
      } catch (error) {
        console.error('获取用户信息失败:', error)
        // 如果是认证错误，清除登录状态
        if (error.message.includes('401') || error.message.includes('登录已过期')) {
          this.logout()
        }
        throw error
      }
    },

    logout() {
      console.log('用户退出登录')
      this.token = null
      this.userInfo = null
      this.initialized = false
      localStorage.removeItem('token')
      localStorage.removeItem('userId')
    },

    // 检查并刷新登录状态
    async refreshAuthState() {
      if (!this.token) return false

      const isValid = await this.validateToken()
      if (!isValid) {
        this.logout()
        return false
      }

      // 如果没有用户信息，尝试获取
      if (!this.userInfo) {
        try {
          await this.fetchUserInfo()
        } catch (error) {
          this.logout()
          return false
        }
      }

      return true
    },
    
    // 添加收藏景点
    async addFavorite(attractionId) {
      if (!this.isLoggedIn || !this.userId) {
        throw new Error('请先登录')
      }
      return await userApi.addFavorite(this.userId, attractionId)
    },
    
    // 取消收藏景点
    async removeFavorite(attractionId) {
      if (!this.isLoggedIn || !this.userId) {
        throw new Error('请先登录')
      }
      return await userApi.removeFavorite(this.userId, attractionId)
    },
    
    // 获取用户收藏的景点
    async getFavorites() {
      if (!this.isLoggedIn || !this.userId) {
        return []
      }
      return await userApi.getUserFavorites(this.userId)
    },
    
    // 获取用户评论
    async getReviews() {
      if (!this.isLoggedIn || !this.userId) {
        return []
      }
      return await userApi.getUserReviews(this.userId)
    },
    // 添加更新用户信息的方法
    async updateUser(userId, userData) {
      if (!this.isLoggedIn || !userId) {
        throw new Error('请先登录')
      }
      
      try {
        const updatedUser = await userApi.updateUser(userId, userData)
        this.userInfo = updatedUser
        return updatedUser
      } catch (error) {
        console.error('更新用户信息失败:', error)
        throw error
      }
    }
  }
})
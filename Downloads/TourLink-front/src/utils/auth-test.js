// 用于测试登录状态管理的工具函数
import { useUserStore } from '../stores/user'

export const testAuthFlow = async () => {
  const userStore = useUserStore()
  
  console.log('=== 开始测试登录状态管理 ===')
  
  // 测试1: 检查初始状态
  console.log('1. 初始状态检查:')
  console.log('   - isLoggedIn:', userStore.isLoggedIn)
  console.log('   - token:', userStore.token ? '存在' : '不存在')
  console.log('   - userInfo:', userStore.userInfo ? '存在' : '不存在')
  console.log('   - initialized:', userStore.initialized)
  
  // 测试2: 检查localStorage
  console.log('2. localStorage检查:')
  console.log('   - token:', localStorage.getItem('token') ? '存在' : '不存在')
  console.log('   - userId:', localStorage.getItem('userId') ? '存在' : '不存在')
  
  // 测试3: 如果有token，验证其有效性
  if (userStore.token) {
    console.log('3. Token有效性验证:')
    try {
      const isValid = await userStore.validateToken()
      console.log('   - Token有效性:', isValid ? '有效' : '无效')
      
      if (isValid && !userStore.userInfo) {
        console.log('   - 尝试获取用户信息...')
        await userStore.fetchUserInfo()
        console.log('   - 用户信息获取:', userStore.userInfo ? '成功' : '失败')
      }
    } catch (error) {
      console.log('   - Token验证失败:', error.message)
    }
  }
  
  // 测试4: 检查最终状态
  console.log('4. 最终状态:')
  console.log('   - isLoggedIn:', userStore.isLoggedIn)
  console.log('   - 用户名:', userStore.userInfo?.username || '未获取')
  
  console.log('=== 登录状态管理测试完成 ===')
}

// 在浏览器控制台中可以调用的测试函数
window.testAuth = testAuthFlow

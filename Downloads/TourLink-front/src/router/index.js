import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '../stores/user'

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: 'Home',
      component: () => import('../views/home/<USER>')
    },
    {
      path: '/spots',
      name: 'Spots',
      component: () => import('../views/spots/index.vue')
    },
    {
      path: '/spots/:id',
      name: 'SpotDetail',
      component: () => import('../views/spots/detail.vue')
    },
    {
      path: '/plan',
      name: 'Plan',
      component: () => import('../views/plan/index.vue')
    },
    {
      path: '/guides',
      name: 'Guides',
      component: () => import('../views/guides/index.vue')
    },
    {
      path: '/guides/edit/:id?',
      name: 'GuideEdit',
      component: () => import('../views/guides/edit.vue'),
      meta: { requiresAuth: true } // 添加路由元信息
    },
    {
      path: '/guides/:id',        // 详情路由放在后面
      name: 'GuideDetail',
      component: () => import('../views/guides/detail.vue')
    },
    // 在路由配置中添加requiresAuth元信息
    {
      path: '/profile',
      name: 'Profile',
      component: () => import('../views/profile/index.vue'),
      meta: { requiresAuth: true }
    },
    {
      path: '/login',
      name: 'Login',
      component: () => import('../views/auth/login.vue')
    },
    {
      path: '/register',
      name: 'Register',
      component: () => import('../views/auth/register.vue')
    },
    {
      path: '/:pathMatch(.*)*',
      name: 'NotFound',
      component: () => import('../views/NotFound.vue')
    }
  ]
})

router.beforeEach(async (to, from, next) => {
  const userStore = useUserStore()

  // 需要登录的路由
  const requiresAuth = to.matched.some(record => record.meta.requiresAuth)

  // 如果需要登录验证
  if (requiresAuth) {
    // 如果没有token，直接跳转到登录页
    if (!userStore.token) {
      console.log('没有token，跳转到登录页')
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 如果有token但没有用户信息，尝试获取用户信息
    if (!userStore.userInfo) {
      console.log('有token但没有用户信息，尝试获取用户信息')
      try {
        // 先验证token是否有效
        const isValid = await userStore.validateToken()
        if (!isValid) {
          console.log('Token无效，跳转到登录页')
          userStore.logout()
          next({
            path: '/login',
            query: { redirect: to.fullPath }
          })
          return
        }

        // Token有效，获取用户信息
        await userStore.fetchUserInfo()
        console.log('用户信息获取成功，继续导航')
      } catch (error) {
        console.log('获取用户信息失败:', error.message)
        // 如果是认证相关错误，清除登录状态并跳转
        if (error.message.includes('401') || error.message.includes('登录已过期')) {
          userStore.logout()
          next({
            path: '/login',
            query: { redirect: to.fullPath }
          })
          return
        }
        // 其他错误，允许继续导航但记录错误
        console.warn('获取用户信息失败，但允许继续导航:', error.message)
      }
    }

    console.log('登录验证通过，继续导航到:', to.path)
  }

  // 如果已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && userStore.isLoggedIn) {
    next('/')
    return
  }

  next()
})


router.onError((error) => {
  console.error('路由错误:', error)
  // 只有在特定情况下才重定向
  if (error.type === 'chunk-load-error') {
    // 只有在代码分割加载失败等严重错误时才重定向
    router.push({
      name: 'Error',
      params: {
        errorMessage: '系统错误',
        errorDetails: error.message || '加载页面时发生错误'
      }
    })
  }
})

// 确保Error路由已添加
if (!router.hasRoute('Error')) {
  router.addRoute({
    path: '/error',
    name: 'Error',
    component: () => import('../views/Error.vue'),
    props: true
  })
}

export default router
